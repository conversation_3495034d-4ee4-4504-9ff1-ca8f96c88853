import socket

def udp_client_test():
    # 创建UDP socket
    client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    # 服务器地址
    server_address = ('**************', 31445)
    
    try:
        # 发送测试数据
        test_message = "Header=CheckJailbreakWhitelist&UDID=00008103-000D65D61A60801E&timestamp=1751518887&signature=541d4529c079aba1b842ff24b541de5a54fe932bb62e74807bf38e533a7c5172&code=DFZKZMJEEPKIDMUSIVUVSE"
        print(f"发送消息到服务器: {test_message}")
        client_socket.sendto(test_message.encode('utf-8'), server_address)
        
        # 接收响应
        response, server_addr = client_socket.recvfrom(1024)
        print(f"收到服务器响应: {response.decode('utf-8')}")
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        client_socket.close()

if __name__ == "__main__":
    udp_client_test() 