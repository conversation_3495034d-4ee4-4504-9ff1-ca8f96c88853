//
//  udp.m
//  udp_hook
//
//  Created by 小七 on 2025/6/29.
//

#import "udp.h"
#import "CaptainHook.h"
#import "fishhook.h"
#import <sys/socket.h>
#import <netinet/in.h>
#import <arpa/inet.h>
#import <dlfcn.h>
#import <CommonCrypto/CommonCryptor.h>
@implementation udp

// 保存原始函数指针
static ssize_t (*original_sendto)(int socket, const void *buffer, size_t length, int flags, const struct sockaddr *dest_addr, socklen_t dest_len);

// 保存原始 CCCrypt 函数指针
static CCCryptorStatus (*original_CCCrypt)(CCOperation op, CCAlgorithm alg, CCOptions options, const void *key, size_t keyLength, const void *iv, const void *dataIn, size_t dataInLength, void *dataOut, size_t dataOutAvailable, size_t *dataOutMoved);

// hook后的sendto函数
static ssize_t hooked_sendto(int socket, const void *buffer, size_t length, int flags, const struct sockaddr *dest_addr, socklen_t dest_len) {

    struct sockaddr_storage modified_addr;
    const struct sockaddr *final_addr = dest_addr;
    socklen_t final_len = dest_len;
    
    if (dest_addr && dest_addr->sa_family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)dest_addr;
        char ip_str[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(addr_in->sin_addr), ip_str, INET_ADDRSTRLEN);
        
        if (strcmp(ip_str, "***************") == 0) {
            struct sockaddr_in *new_addr = (struct sockaddr_in *)&modified_addr;
            memcpy(new_addr, addr_in, sizeof(struct sockaddr_in));
            inet_pton(AF_INET, "**************", &(new_addr->sin_addr));

            final_addr = (struct sockaddr *)new_addr;
            final_len = sizeof(struct sockaddr_in);
        }
        //        if (strcmp(ip_str, "*************") == 0) {
        //            struct sockaddr_in *new_addr = (struct sockaddr_in *)&modified_addr;
        //            memcpy(new_addr, addr_in, sizeof(struct sockaddr_in));
        //            inet_pton(AF_INET, "**************", &(new_addr->sin_addr));
        //
        //            final_addr = (struct sockaddr *)new_addr;
        //            final_len = sizeof(struct sockaddr_in);
        //        }
        
        
//
//        if (ntohs(addr_in->sin_port) == 3817 && buffer && length > 0) {
//            NSString *dataString = [[NSString alloc] initWithBytes:buffer length:length encoding:NSUTF8StringEncoding];
//            NSArray *components = [dataString componentsSeparatedByString:@"&"];
//            NSString *serialNumber = components[0];
//            NSString *replacedString = [dataString stringByReplacingOccurrencesOfString:serialNumber withString:wx_zyyz()];
//            NSData *modifiedData = [replacedString dataUsingEncoding:NSUTF8StringEncoding];
//            if (modifiedData) {
//                ssize_t result = original_sendto(socket, modifiedData.bytes, modifiedData.length, flags, final_addr, final_len);
//                return result;
//            }
//        }
    }
    ssize_t result = original_sendto(socket, buffer, length, flags, final_addr, final_len);
    
    return result;
}

// hook后的CCCrypt函数
static CCCryptorStatus hooked_CCCrypt(CCOperation op, CCAlgorithm alg, CCOptions options, const void *key, size_t keyLength, const void *iv, const void *dataIn, size_t dataInLength, void *dataOut, size_t dataOutAvailable, size_t *dataOutMoved) {

    // 记录加密操作信息
    NSString *operation = (op == kCCEncrypt) ? @"加密" : @"解密";
    NSString *algorithm = @"未知";

    switch (alg) {
        case kCCAlgorithmAES128:
        case kCCAlgorithmAES:
            algorithm = @"AES";
            break;
        case kCCAlgorithmDES:
            algorithm = @"DES";
            break;
        case kCCAlgorithm3DES:
            algorithm = @"3DES";
            break;
        case kCCAlgorithmCAST:
            algorithm = @"CAST";
            break;
        case kCCAlgorithmRC4:
            algorithm = @"RC4";
            break;
        case kCCAlgorithmRC2:
            algorithm = @"RC2";
            break;
        case kCCAlgorithmBlowfish:
            algorithm = @"Blowfish";
            break;
        default:
            algorithm = [NSString stringWithFormat:@"未知算法(%d)", alg];
            break;
    }

    NSLog(@"[HOOK] CCCrypt 调用 - 操作: %@, 算法: %@, 密钥长度: %zu, 数据长度: %zu", operation, algorithm, keyLength, dataInLength);

    // 打印完整密钥信息
    if (key && keyLength > 0) {
        NSString *keyString = [[NSString alloc] initWithBytes:key length:keyLength encoding:NSUTF8StringEncoding];
        if (keyString) {
            NSLog(@"[HOOK] CCCrypt 密钥字符串: %@", keyString);
        } else {
            NSLog(@"[HOOK] CCCrypt 密钥(非UTF8): %.*s", (int)keyLength, (const char *)key);
        }
    }

    // 打印完整IV信息
    if (iv) {
        // 根据算法确定IV长度
        size_t ivLength = 0;
        switch (alg) {
            case kCCAlgorithmAES128:
            case kCCAlgorithmAES:
                ivLength = kCCBlockSizeAES128;
                break;
            case kCCAlgorithmDES:
                ivLength = kCCBlockSizeDES;
                break;
            case kCCAlgorithm3DES:
                ivLength = kCCBlockSize3DES;
                break;
            case kCCAlgorithmBlowfish:
                ivLength = kCCBlockSizeBlowfish;
                break;
            default:
                ivLength = 16; // 默认16字节
                break;
        }

        NSString *ivString = [[NSString alloc] initWithBytes:iv length:ivLength encoding:NSUTF8StringEncoding];
        if (ivString) {
            NSLog(@"[HOOK] CCCrypt IV字符串: %@", ivString);
        } else {
            NSLog(@"[HOOK] CCCrypt IV(非UTF8): %.*s", (int)ivLength, (const char *)iv);
        }
    }

    // 打印完整输入数据信息
    if (dataIn && dataInLength > 0) {
        NSData *inputData = [NSData dataWithBytes:dataIn length:dataInLength];
        NSLog(@"[HOOK] CCCrypt 完整输入数据: %@", inputData);

        // 同时打印十六进制字符串格式
        NSMutableString *inputHexString = [NSMutableString string];
        const unsigned char *inputBytes = (const unsigned char *)dataIn;
        for (size_t i = 0; i < dataInLength; i++) {
            [inputHexString appendFormat:@"%02X", inputBytes[i]];
        }
        NSLog(@"[HOOK] CCCrypt 输入数据(十六进制): %@", inputHexString);
    }

    // 调用原始函数
    CCCryptorStatus result = original_CCCrypt(op, alg, options, key, keyLength, iv, dataIn, dataInLength, dataOut, dataOutAvailable, dataOutMoved);

    // 打印完整输出数据信息
    if (result == kCCSuccess && dataOut && dataOutMoved && *dataOutMoved > 0) {
        NSData *outputData = [NSData dataWithBytes:dataOut length:*dataOutMoved];
        NSLog(@"[HOOK] CCCrypt 完整输出数据: %@, 输出长度: %zu", outputData, *dataOutMoved);

        // 同时打印十六进制字符串格式
        NSMutableString *outputHexString = [NSMutableString string];
        const unsigned char *outputBytes = (const unsigned char *)dataOut;
        for (size_t i = 0; i < *dataOutMoved; i++) {
            [outputHexString appendFormat:@"%02X", outputBytes[i]];
        }
        NSLog(@"[HOOK] CCCrypt 输出数据(十六进制): %@", outputHexString);
    }

    NSLog(@"[HOOK] CCCrypt 结果: %d", result);

    return result;
}

NSString * wx_zyyz(void){
    NSString *serialNumber = nil;
    void *IOKit = dlopen("/System/Library/Frameworks/IOKit.framework/IOKit", RTLD_NOW);
    if (IOKit)
    {
        mach_port_t *kIOMasterPortDefault = dlsym(IOKit, "kIOMasterPortDefault");
        CFMutableDictionaryRef (*IOServiceMatching)(const char *name) = dlsym(IOKit, "IOServiceMatching");
        mach_port_t (*IOServiceGetMatchingService)(mach_port_t masterPort, CFDictionaryRef matching) = dlsym(IOKit, "IOServiceGetMatchingService");
        CFTypeRef (*IORegistryEntryCreateCFProperty)(mach_port_t entry, CFStringRef key, CFAllocatorRef allocator, uint32_t options) = dlsym(IOKit, "IORegistryEntryCreateCFProperty");
        kern_return_t (*IOObjectRelease)(mach_port_t object) = dlsym(IOKit, "IOObjectRelease");
        
        if (kIOMasterPortDefault && IOServiceGetMatchingService && IORegistryEntryCreateCFProperty && IOObjectRelease)
        {
            mach_port_t platformExpertDevice = IOServiceGetMatchingService(*kIOMasterPortDefault, IOServiceMatching("IOPlatformExpertDevice"));
            if (platformExpertDevice)
            {
                CFTypeRef platformSerialNumber = IORegistryEntryCreateCFProperty(platformExpertDevice, CFSTR("IOPlatformSerialNumber"), kCFAllocatorDefault, 0);
                if (CFGetTypeID(platformSerialNumber) == CFStringGetTypeID())
                {
                    serialNumber = [NSString stringWithString:(__bridge NSString*)platformSerialNumber];
                    CFRelease(platformSerialNumber);
                }
                IOObjectRelease(platformExpertDevice);
            }
        }
        dlclose(IOKit);
    }
    return serialNumber;
    
}


CHDeclareClass(UILabel);
CHMethod1(void, UILabel, setText, NSString*, title)

{

    if([title containsString:@"skype"]){
        title = @"逆向破解tg:wx_zyyz";

    }

    return CHSuper1(UILabel, setText,title);

    
}
// 定义原始dlsym函数指针
static void *(*orig_dlsym)(void *handle, const char *symbol);


// 替换的dlsym函数实现
static void *replaced_dlsym(void *handle, const char *symbol) {

//    if (strcmp(symbol, "objc_getClass") == 0) {
//        return orig_dlsym(handle, symbol);
//    }
    NSLog(@"[HOOK] dlsym 调用，符号名称: %s", symbol);
//
//    if (strcmp(symbol, "MGCopyAnswer") == 0) {
//        NSLog(@"[HOOK] 检测到MGCopyAnswer调用，返回自定义实现");
//        return (void *)custom_MGCopyAnswer;
//    }

    
    void *result = orig_dlsym(handle, symbol);

    return result;
}

static void __attribute__((constructor)) ooo() {
    CHLoadLateClass(UILabel);
    CHHook1(UILabel, setText);

    // Hook sendto 函数
    struct rebinding sendto_rebinding = {"sendto", hooked_sendto, (void *)&original_sendto};
    rebind_symbols(&sendto_rebinding, 1);

    // Hook CCCrypt 函数
    struct rebinding cccrypt_rebinding = {"CCCrypt", hooked_CCCrypt, (void *)&original_CCCrypt};
    rebind_symbols(&cccrypt_rebinding, 1);

    [udp xxx];
}

+(void)xxx{
    struct rebinding dlsym_rebinding = {"dlsym", (void *)replaced_dlsym, (void **)&orig_dlsym};
    struct rebinding rebindings[] = {dlsym_rebinding};
    rebind_symbols(rebindings, 1);
}

@end
