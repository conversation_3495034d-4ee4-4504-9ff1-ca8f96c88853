import socket
import threading
import time
import hashlib

def xor_encrypt(plaintext, key_hex):
    """
    使用XOR加密明文
    :param plaintext: 明文字符串
    :param key_hex: 16进制密钥字符串
    :return: 加密后的16进制字符串
    """
    # 将16进制密钥转换为字节
    key_bytes = bytes.fromhex(key_hex)
    
    # 将明文转换为字节
    plaintext_bytes = plaintext.encode('utf-8')
    
    # XOR加密
    encrypted_bytes = bytearray()
    for i, byte in enumerate(plaintext_bytes):
        # 使用密钥循环进行XOR操作
        key_byte = key_bytes[i % len(key_bytes)]
        encrypted_bytes.append(byte ^ key_byte)
    
    # 返回加密后的16进制字符串
    return encrypted_bytes.hex()

def xor_decrypt(encrypted_hex, key_hex):
    """
    使用XOR解密密文
    :param encrypted_hex: 加密后的16进制字符串
    :param key_hex: 16进制密钥字符串
    :return: 解密后的明文字符串
    """
    # 将16进制密钥和密文转换为字节
    key_bytes = bytes.fromhex(key_hex)
    encrypted_bytes = bytes.fromhex(encrypted_hex)
    
    # XOR解密（XOR的特性：A XOR B XOR B = A）
    decrypted_bytes = bytearray()
    for i, byte in enumerate(encrypted_bytes):
        key_byte = key_bytes[i % len(key_bytes)]
        decrypted_bytes.append(byte ^ key_byte)
    
    # 返回解密后的明文
    return decrypted_bytes.decode('utf-8')

    
def udp_server():
    # 创建UDP socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    # 绑定地址和端口
    server_address = ('0.0.0.0', 3819)
    server_socket.bind(server_address)
    
    print(f"UDP服务器已启动，监听端口 {server_address[1]}")
    print("等待客户端连接...")
    
    try:
        while True:
            # 接收数据
            data, client_address = server_socket.recvfrom(1024)
            
            print(f"收到来自 {client_address} 的数据: {data.decode('utf-8', errors='ignore')}")
            
            # 将16进制20202020转换为字符串
            hex_value = 0x20202020
            # 转换为4字节的字符串
            response = hex_value.to_bytes(4, byteorder='big').decode('ascii')
            
            server_socket.sendto(response.encode('utf-8'), client_address)
            
            print(f"已向 {client_address} 发送响应: {repr(response)} (16进制20202020转换的字符串)")
            
    except KeyboardInterrupt:
        print("\n服务器正在关闭...")
    finally:
        server_socket.close()
        print("服务器已关闭")

def udp_server2():
    # 创建UDP socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    # 绑定地址和端口
    server_address = ('0.0.0.0', 3817)
    server_socket.bind(server_address)
    
    print(f"UDP服务器2已启动，监听端口 {server_address[1]}")
    print("等待客户端连接...")
    
    # XOR密钥
    xor_key = "4f0afb05ed5bc546f4f5b97b5e73f759e0ba06d5b3a30db6fcf83bb1fa1f838e7dcee5336bdadd311b1e1868800a38cb7e171a1bf5742b05e46ef556b06c18a8add62cd0be670849"
    
    try:
        while True:
            # 接收数据
            data, client_address = server_socket.recvfrom(1024)
            
            print(f"收到来自 {client_address} 的数据: {data.decode('utf-8', errors='ignore')}")
            
            # 生成时间戳毫秒
            timestamp_ms = int(time.time() * 1000)
            
            # 固定值
            fixed_value = "56501"
            
            # 生成MD5哈希: 49565-时间戳毫秒
            md5_input = f"49565-{timestamp_ms}"
            md5_hash = hashlib.md5(md5_input.encode('utf-8')).hexdigest()
            
            # 固定时间
            fixed_time = "2030-06-29 22:24:35"
            
            # 构造响应数据
            response_data = f"{timestamp_ms},{fixed_value},{md5_hash},{fixed_time}"
            
            print(f"明文响应数据: {response_data}")
            
            # XOR加密
            encrypted_response = xor_encrypt(response_data, xor_key)
            
            # 转换为大写
            encrypted_response = encrypted_response.upper()
            
            # 拼接############
            final_response = encrypted_response + "############"
            
            print(f"加密后的响应数据: {final_response}")
            
            # 发送加密后的数据
            server_socket.sendto(final_response.encode('utf-8'), client_address)
            
            print(f"已向 {client_address} 发送加密响应")
            
    except KeyboardInterrupt:
        print("\n服务器2正在关闭...")
    finally:
        server_socket.close()
        print("服务器2已关闭")

if __name__ == "__main__":
    # 创建两个线程分别运行两个服务器
    thread1 = threading.Thread(target=udp_server2, name="UDP-Server-3817")
    thread2 = threading.Thread(target=udp_server, name="UDP-Server-3819")
    
    # 设置为守护线程，这样主程序退出时线程也会退出
    thread1.daemon = True
    thread2.daemon = True
    
    # 启动两个线程
    print("正在启动UDP服务器...")
    thread1.start()
    thread2.start()
    
    print("两个UDP服务器已启动:")
    print("- 服务器1: 端口3817 (加密服务)")
    print("- 服务器2: 端口3819 (简单响应)")
    print("按 Ctrl+C 退出")
    
    try:
        # 主线程等待，直到用户按Ctrl+C
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在关闭所有服务器...")
        print("服务器已关闭")